substitutions:
    trigger_pin: GPIO14
    echo_pin: GPIO15
    update_interval: 5s
    water_tank_height: '200' # Chi<PERSON><PERSON> cao bồn nư<PERSON> (cm)
    sensor_height_from_bottom: '10' # K<PERSON><PERSON><PERSON> cách từ cảm biến đến đ<PERSON>y bồn (cm)

sensor:
    # Cảm biến siêu âm đo khoảng cách
    - platform: ultrasonic
      id: water_distance_raw
      trigger_pin: $trigger_pin
      echo_pin: $echo_pin
      update_interval: $update_interval
      timeout: 4m
      pulse_time: 20us
      unit_of_measurement: 'cm'
      accuracy_decimals: 1
      internal: true # Ẩn sensor này, chỉ dùng để tính toán
      filters:
          - multiply: 100 # <PERSON><PERSON>ển từ m sang cm
          - median:
                window_size: 5
                send_every: 3
          - delta: 2.0 # Chỉ cập nhật khi thay đổi >= 2cm

    # Độ cao mực nước thực tế
    - platform: template
      id: water_level_height
      name: 'Water Level Height'
      unit_of_measurement: 'cm'
      device_class: 'distance'
      state_class: 'measurement'
      accuracy_decimals: 1
      icon: 'mdi:waves'
      lambda: |-
          float distance = id(water_distance_raw).state;
          float tank_height = ${water_tank_height};
          float sensor_offset = ${sensor_height_from_bottom};

          if (isnan(distance)) {
            return NAN;
          }

          // Tính độ cao mực nước = chiều cao bồn - khoảng cách đo được - offset cảm biến
          float water_height = tank_height - distance - sensor_offset;

          // Đảm bảo giá trị không âm và không vượt quá chiều cao bồn
          if (water_height < 0) water_height = 0;
          if (water_height > tank_height) water_height = tank_height;

          return water_height;
      update_interval: $update_interval

    # Phần trăm mực nước
    - platform: template
      id: water_level_percentage
      name: 'Water Level Percentage'
      unit_of_measurement: '%'
      device_class: 'humidity' # Sử dụng device_class này để hiển thị % trong HA
      state_class: 'measurement'
      accuracy_decimals: 0
      icon: 'mdi:water-percent'
      lambda: |-
          float water_height = id(water_level_height).state;
          float tank_height = ${water_tank_height};

          if (isnan(water_height)) {
            return NAN;
          }

          // Tính phần trăm = (độ cao nước / chiều cao bồn) * 100
          float percentage = (water_height / tank_height) * 100.0;

          // Đảm bảo giá trị trong khoảng 0-100%
          if (percentage < 0) percentage = 0;
          if (percentage > 100) percentage = 100;

          return percentage;
      update_interval: $update_interval

    # Khoảng cách thô (để debug)
    - platform: template
      id: water_distance_debug
      name: 'Water Distance (Debug)'
      unit_of_measurement: 'cm'
      device_class: 'distance'
      state_class: 'measurement'
      accuracy_decimals: 1
      icon: 'mdi:ruler'
      entity_category: 'diagnostic'
      lambda: |-
          return id(water_distance_raw).state;
      update_interval: $update_interval
